---
name: Bug report
about: Create a report to help us improve
title: '[BUG] '
labels: 'bug'
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Device Information:**
 - Device: [e.g. iPhone 12, Samsung Galaxy S21, Desktop]
 - OS: [e.g. iOS 15.0, Android 12, Windows 11]
 - Browser [if web]: [e.g. Chrome 96, Safari 15]
 - App Version: [e.g. 1.0.0]

**Flutter Information:**
 - Flutter Version: [e.g. 3.7.0]
 - Dart Version: [e.g. 3.0.0]

**Additional context**
Add any other context about the problem here.

**Error <PERSON>gs**
If applicable, paste any error logs or stack traces here:
```
Paste error logs here
```

**Possible Solution**
If you have any ideas on how to fix this issue, please describe them here.
