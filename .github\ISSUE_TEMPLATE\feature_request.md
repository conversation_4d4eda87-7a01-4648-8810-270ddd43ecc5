---
name: Feature request
about: Suggest an idea for this project
title: '[FEATURE] '
labels: 'enhancement'
assignees: ''

---

**Is your feature request related to a problem? Please describe.**
A clear and concise description of what the problem is. Ex. I'm always frustrated when [...]

**Describe the solution you'd like**
A clear and concise description of what you want to happen.

**Describe alternatives you've considered**
A clear and concise description of any alternative solutions or features you've considered.

**User Story**
As a [type of user], I want [goal] so that [benefit].

**Acceptance Criteria**
- [ ] Criterion 1
- [ ] Criterion 2
- [ ] Criterion 3

**Mockups/Wireframes**
If applicable, add mockups or wireframes to help explain your feature request.

**Additional context**
Add any other context or screenshots about the feature request here.

**Implementation Considerations**
- Impact on existing features
- Technical complexity
- Required dependencies
- Performance implications

**Priority**
- [ ] Low
- [ ] Medium
- [ ] High
- [ ] Critical
