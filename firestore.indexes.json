{"indexes": [{"collectionGroup": "slots", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doctorId", "order": "ASCENDING"}, {"fieldPath": "isBooked", "order": "ASCENDING"}, {"fieldPath": "dateTime", "order": "ASCENDING"}]}, {"collectionGroup": "slots", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doctorId", "order": "ASCENDING"}, {"fieldPath": "dateTime", "order": "ASCENDING"}]}, {"collectionGroup": "appointments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doctorId", "order": "ASCENDING"}, {"fieldPath": "dateTime", "order": "ASCENDING"}]}, {"collectionGroup": "appointments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "patientId", "order": "ASCENDING"}, {"fieldPath": "dateTime", "order": "ASCENDING"}]}, {"collectionGroup": "appointments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "patientId", "order": "ASCENDING"}, {"fieldPath": "dateTime", "order": "DESCENDING"}]}], "fieldOverrides": []}